/**
 * Módulo para gestionar la carga y acceso a los datos de juegos y aplicaciones
 */

// Almacenamiento global para los juegos y apps
let items = [];

/**
 * Carga los datos de juegos y aplicaciones desde el archivo JSON
 * @returns {Promise} Promesa que se resuelve cuando los datos están cargados
 */
async function cargarDatos() {
    try {
        // Intentar primero con la ruta más probable para evitar cargar datos incorrectos
        const response = await fetch('./data/info.json');
        if (response.ok) {
            items = await response.json();
            return items;
        }

        // Si la primera ruta falla, intentar con una ruta alternativa
        const altResponse = await fetch('../data/info.json');
        if (altResponse.ok) {
            items = await altResponse.json();
            return items;
        }

        // Si ambas rutas fallan, lanzar un error
        throw new Error('No se pudo cargar el archivo info.json');
    } catch (error) {
        console.error('Error al cargar los datos:', error);
        return []; // Devolver array vacío en caso de error
    }
}

/**
 * Obtiene todos los items cargados
 * @returns {Array} Array de objetos de juegos y aplicaciones
 */
function obtenerTodosLosItems() {
    return items;
}

/**
 * Filtra items por categoría (juego o aplicacion)
 * @param {String} categoria Categoría para filtrar ('juego' o 'aplicacion')
 * @returns {Array} Array de items que coinciden con la categoría
 */
function filtrarPorCategoria(categoria) {
    return items.filter(item => item.categoria === categoria);
}

/**
 * Filtra items populares
 * @returns {Array} Array de items populares
 */
function filtrarPopulares() {
    return items.filter(item => item.esPopular === true);
}

/**
 * Filtra items nuevos
 * @returns {Array} Array de items nuevos
 */
function filtrarNuevos() {
    return items.filter(item => item.esNuevo === true);
}

/**
 * Filtra items actualizados
 * @returns {Array} Array de items actualizados
 */
function filtrarActualizados() {
    return items.filter(item => item.esActualizado === true);
}

/**
 * Obtiene un item por su ID
 * @param {String} id ID del item
 * @returns {Object|null} Objeto del item o null si no se encuentra
 */
function obtenerItemPorId(id) {
    return items.find(item => item.id === id) || null;
}

/**
 * Obtiene items relacionados a un item específico
 * @param {String} idActual ID del item actual para excluirlo
 * @param {Number} cantidad Cantidad de items a devolver
 * @returns {Array} Array de items relacionados
 */
function obtenerItemsRelacionados(idActual, cantidad = 3) {
    // Excluimos el item actual y seleccionamos aleatoriamente
    const itemsDisponibles = items.filter(item => item.id !== idActual);

    // Mezclar los items de forma aleatoria
    const itemsMezclados = [...itemsDisponibles].sort(() => Math.random() - 0.5);

    // Devolver la cantidad solicitada o todos si hay menos que la cantidad
    return itemsMezclados.slice(0, Math.min(cantidad, itemsMezclados.length));
}