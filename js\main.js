/**
 * Funcionalidad principal para GameHub
 */

// Estado actual de la aplicación
let currentTab = 'juegos';
let carouselStates = {
    recomendados: { currentPage: 0, totalPages: 0 },
    actualizados: { currentPage: 0, totalPages: 0 },
    nuevos: { currentPage: 0, totalPages: 0 }
};

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', inicializarApp);

/**
 * Inicializa la aplicación
 */
async function inicializarApp() {
    // Comprobar en qué página estamos
    const path = window.location.pathname;
    if (path.includes('info.html')) {
        // Si estamos en la página de detalles, no hacemos nada
        // La inicialización se hace en info.js
        return;
    }

    // Cargar datos de juegos y apps (solo si la función existe)
    if (typeof cargarDatos === 'function') {
        await cargarDatos();
    } else {
        console.log('Función cargarDatos no disponible en esta página');
    }

    // Configurar sliding tabs
    configurarSlidingTabs();

    // Cargar contenido inicial (tab de juegos)
    cargarContenidoJuegos();

    // Configurar eventos de navegación
    configurarNavegacion();
}

/**
 * Configura el sistema de sliding tabs
 */
function configurarSlidingTabs() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabIndicator = document.querySelector('.tab-indicator');

    tabItems.forEach((tab, index) => {
        tab.addEventListener('click', () => {
            // Remover active de todos los tabs
            tabItems.forEach(t => t.classList.remove('active'));
            
            // Agregar active al tab clickeado
            tab.classList.add('active');
            
            // Mover el indicador correctamente
            if (tabIndicator) {
                tabIndicator.style.transform = `translateX(${index * 100}%)`;
            }
            
            // Cambiar contenido
            const tabType = tab.getAttribute('data-tab');
            cambiarTab(tabType);
        });
    });
}

/**
 * Cambia el contenido según el tab seleccionado
 * @param {String} tabType Tipo de tab ('juegos', 'aplicaciones', 'online')
 */
function cambiarTab(tabType) {
    currentTab = tabType;
    
    // Ocultar todos los contenidos
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Mostrar contenido correspondiente
    const targetContent = document.getElementById(`tab-${tabType}`);
    if (targetContent) {
        targetContent.classList.remove('hidden');
    }
    
    // Cargar contenido específico
    switch(tabType) {
        case 'juegos':
            cargarContenidoJuegos();
            break;
        case 'aplicaciones':
            cargarContenidoAplicaciones();
            break;
        case 'online':
            cargarContenidoOnline();
            break;
    }
}

/**
 * Carga el contenido de la sección de juegos
 */
function cargarContenidoJuegos() {
    cargarSeccionRecomendados();
    cargarSeccionActualizados();
    cargarSeccionNuevos();
}

/**
 * Carga la sección de juegos recomendados (layout 2x2)
 */
function cargarSeccionRecomendados() {
    const juegosRecomendados = filtrarPopulares().filter(item => item.categoria === 'juego');
    const contenedor = document.getElementById('recomendados-container');
    if (contenedor) {
        renderizarJuegos2x2(juegosRecomendados, contenedor, 'recomendados');
    }
}

/**
 * Carga la sección de juegos actualizados (layout 2x1)
 */
function cargarSeccionActualizados() {
    const juegosActualizados = filtrarActualizados().filter(item => item.categoria === 'juego');
    const contenedor = document.getElementById('actualizados-container');
    if (contenedor) {
        renderizarJuegos2x1(juegosActualizados, contenedor, 'actualizados');
    }
}

/**
 * Carga la sección de juegos nuevos (layout 1x1)
 */
function cargarSeccionNuevos() {
    const juegosNuevos = filtrarNuevos().filter(item => item.categoria === 'juego');
    const contenedor = document.getElementById('nuevos-container');
    if (contenedor) {
        renderizarJuegos1x1(juegosNuevos, contenedor, 'nuevos');
    }
}

/**
 * Renderiza juegos en formato 2x2 (Recomendados)
 * @param {Array} juegos Array de objetos de juegos
 * @param {HTMLElement} contenedor Elemento DOM donde se renderizarán los juegos
 * @param {String} sectionId ID de la sección para los indicadores
 */
function renderizarJuegos2x2(juegos, contenedor, sectionId) {
    contenedor.innerHTML = '';
    
    // Agrupar juegos de 4 en 4
    const grupos = [];
    for (let i = 0; i < juegos.length; i += 4) {
        grupos.push(juegos.slice(i, i + 4));
    }
    
    // Actualizar estado del carousel
    carouselStates[sectionId].totalPages = grupos.length;
    carouselStates[sectionId].currentPage = 0;

    grupos.forEach((grupo, index) => {
        const grupoDiv = document.createElement('div');
        grupoDiv.className = 'cards-group-2x2';
        
        grupo.forEach(juego => {
            const gameCard = crearGameCard(juego);
            grupoDiv.appendChild(gameCard);
        });
        
        contenedor.appendChild(grupoDiv);
    });

    // Crear indicadores
    crearIndicadores(sectionId, grupos.length);
    
    // Configurar navegación horizontal
    configurarNavegacionHorizontal(contenedor, sectionId);
}

/**
 * Renderiza juegos en formato 2x1 (Actualizados)
 * @param {Array} juegos Array de objetos de juegos
 * @param {HTMLElement} contenedor Elemento DOM donde se renderizarán los juegos
 * @param {String} sectionId ID de la sección para los indicadores
 */
function renderizarJuegos2x1(juegos, contenedor, sectionId) {
    contenedor.innerHTML = '';
    
    // Agrupar juegos de 2 en 2
    const grupos = [];
    for (let i = 0; i < juegos.length; i += 2) {
        grupos.push(juegos.slice(i, i + 2));
    }
    
    // Actualizar estado del carousel
    carouselStates[sectionId].totalPages = grupos.length;
    carouselStates[sectionId].currentPage = 0;

    grupos.forEach((grupo, index) => {
        const grupoDiv = document.createElement('div');
        grupoDiv.className = 'cards-group-2x1';
        
        grupo.forEach(juego => {
            const gameListItem = crearGameListItem(juego);
            grupoDiv.appendChild(gameListItem);
        });
        
        contenedor.appendChild(grupoDiv);
    });

    // Crear indicadores
    crearIndicadores(sectionId, grupos.length);
    
    // Configurar navegación horizontal
    configurarNavegacionHorizontal(contenedor, sectionId);
}

/**
 * Renderiza juegos en formato 1x1 (Nuevos)
 * @param {Array} juegos Array de objetos de juegos
 * @param {HTMLElement} contenedor Elemento DOM donde se renderizarán los juegos
 * @param {String} sectionId ID de la sección para los indicadores
 */
function renderizarJuegos1x1(juegos, contenedor, sectionId) {
    contenedor.innerHTML = '';
    
    // Actualizar estado del carousel
    carouselStates[sectionId].totalPages = Math.max(1, juegos.length - 2);
    carouselStates[sectionId].currentPage = 0;

    juegos.forEach(juego => {
        const gameCard = crearGameCard(juego);
        contenedor.appendChild(gameCard);
    });

    // Crear indicadores (solo si hay más de 3 elementos)
    if (juegos.length > 3) {
        crearIndicadores(sectionId, Math.ceil(juegos.length / 3));
    }
    
    // Configurar navegación horizontal
    configurarNavegacionHorizontal(contenedor, sectionId);
}

/**
 * Crea una tarjeta de juego
 * @param {Object} juego Objeto del juego
 * @returns {HTMLElement} Elemento de la tarjeta
 */
function crearGameCard(juego) {
    const gameCard = document.createElement('div');
    gameCard.className = 'game-card';
    gameCard.addEventListener('click', () => {
        window.location.href = `info.html?id=${juego.id}`;
    });

    const posterContainer = document.createElement('div');
    posterContainer.className = 'game-poster-container';

    const img = document.createElement('img');
    img.className = 'game-poster';
    img.src = juego.imagen;
    img.alt = juego.nombre;
    img.loading = 'lazy';

    posterContainer.appendChild(img);

    const gameInfo = document.createElement('div');
    gameInfo.className = 'game-info';

    const title = document.createElement('h3');
    title.className = 'game-title';
    title.textContent = juego.nombre;

    const weight = document.createElement('p');
    weight.className = 'game-weight';
    weight.innerHTML = `<i class="fas fa-download"></i> ${juego.peso}`;

    gameInfo.appendChild(title);
    gameInfo.appendChild(weight);

    gameCard.appendChild(posterContainer);
    gameCard.appendChild(gameInfo);

    return gameCard;
}

/**
 * Crea un elemento de lista de juego
 * @param {Object} juego Objeto del juego
 * @returns {HTMLElement} Elemento de la lista
 */
function crearGameListItem(juego) {
    const gameItem = document.createElement('div');
    gameItem.className = 'game-list-item';
    gameItem.addEventListener('click', () => {
        window.location.href = `info.html?id=${juego.id}`;
    });

    const img = document.createElement('img');
    img.className = 'game-list-image';
    img.src = juego.imagen;
    img.alt = juego.nombre;
    img.loading = 'lazy';

    const gameInfo = document.createElement('div');
    gameInfo.className = 'game-list-info';

    const title = document.createElement('h3');
    title.className = 'game-list-title';
    title.textContent = juego.nombre;

    const date = document.createElement('p');
    date.className = 'game-list-date';
    date.innerHTML = `<i class="far fa-clock"></i> ${juego.fechaActualizacion}`;

    const size = document.createElement('p');
    size.className = 'game-list-size';
    size.innerHTML = `<i class="fas fa-download"></i> ${juego.peso}`;

    gameInfo.appendChild(title);
    gameInfo.appendChild(date);
    gameInfo.appendChild(size);

    gameItem.appendChild(img);
    gameItem.appendChild(gameInfo);

    return gameItem;
}

/**
 * Crea los indicadores para un carousel
 * @param {String} sectionId ID de la sección
 * @param {Number} totalPages Número total de páginas
 */
function crearIndicadores(sectionId, totalPages) {
    const indicatorContainer = document.getElementById(`${sectionId}-indicators`);
    if (!indicatorContainer || totalPages <= 1) return;

    indicatorContainer.innerHTML = '';

    for (let i = 0; i < totalPages; i++) {
        const dot = document.createElement('div');
        dot.className = 'indicator-dot';
        if (i === 0) dot.classList.add('active');
        
        dot.addEventListener('click', () => {
            navegarAPagina(sectionId, i);
        });
        
        indicatorContainer.appendChild(dot);
    }
}

/**
 * Configura la navegación horizontal para un contenedor
 * @param {HTMLElement} contenedor El contenedor del carousel
 * @param {String} sectionId ID de la sección
 */
function configurarNavegacionHorizontal(contenedor, sectionId) {
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    // Touch events para móviles
    contenedor.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        isDragging = true;
    });

    contenedor.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        currentX = e.touches[0].clientX;
    });

    contenedor.addEventListener('touchend', (e) => {
        if (!isDragging) return;
        isDragging = false;
        
        const deltaX = startX - currentX;
        if (Math.abs(deltaX) > 50) { // Mínimo deslizamiento de 50px
            if (deltaX > 0) {
                navegarSiguiente(sectionId);
            } else {
                navegarAnterior(sectionId);
            }
        }
    });

    // Mouse events para escritorio
    contenedor.addEventListener('wheel', (e) => {
        if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
            e.preventDefault();
            if (e.deltaX > 0) {
                navegarSiguiente(sectionId);
            } else {
                navegarAnterior(sectionId);
            }
        }
    });
}

/**
 * Navega a la siguiente página del carousel
 * @param {String} sectionId ID de la sección
 */
function navegarSiguiente(sectionId) {
    const state = carouselStates[sectionId];
    if (state.currentPage < state.totalPages - 1) {
        navegarAPagina(sectionId, state.currentPage + 1);
    }
}

/**
 * Navega a la página anterior del carousel
 * @param {String} sectionId ID de la sección
 */
function navegarAnterior(sectionId) {
    const state = carouselStates[sectionId];
    if (state.currentPage > 0) {
        navegarAPagina(sectionId, state.currentPage - 1);
    }
}

/**
 * Navega a una página específica del carousel
 * @param {String} sectionId ID de la sección
 * @param {Number} pageIndex Índice de la página
 */
function navegarAPagina(sectionId, pageIndex) {
    const state = carouselStates[sectionId];
    if (pageIndex < 0 || pageIndex >= state.totalPages) return;

    const contenedor = document.getElementById(`${sectionId}-container`);
    if (!contenedor) return;

    // Actualizar estado
    state.currentPage = pageIndex;

    // Calcular desplazamiento
    const desplazamiento = pageIndex * contenedor.clientWidth;
    contenedor.scrollTo({
        left: desplazamiento,
        behavior: 'smooth'
    });

    // Actualizar indicadores
    actualizarIndicadores(sectionId, pageIndex);
}

/**
 * Actualiza los indicadores activos
 * @param {String} sectionId ID de la sección
 * @param {Number} activePage Página activa
 */
function actualizarIndicadores(sectionId, activePage) {
    const indicatorContainer = document.getElementById(`${sectionId}-indicators`);
    if (!indicatorContainer) return;

    const dots = indicatorContainer.querySelectorAll('.indicator-dot');
    dots.forEach((dot, index) => {
        if (index === activePage) {
            dot.classList.add('active');
        } else {
            dot.classList.remove('active');
        }
    });
}

/**
 * Carga el contenido de aplicaciones (vacío por ahora)
 */
function cargarContenidoAplicaciones() {
    console.log('Contenido de aplicaciones próximamente');
}

/**
 * Carga el contenido online (vacío por ahora)
 */
function cargarContenidoOnline() {
    console.log('Contenido online próximamente');
}

/**
 * Configura la navegación entre páginas
 */
function configurarNavegacion() {
    // Los eventos ya están configurados en las tarjetas
}

/**
 * Utilidad para obtener parámetros de la URL
 * @param {String} nombre Nombre del parámetro
 * @returns {String|null} Valor del parámetro o null si no existe
 */
function obtenerParametroURL(nombre) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(nombre);
}