/* Variables de colores y fuentes */
:root {
    --bg-color: #121212;
    --bg-secondary: #1e1e1e;
    --text-color: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --accent-color: #00ff41;
    --green-accent: #00ff41;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --card-width: 140px;       /* Ancho base de las tarjetas */
    --card-height: 200px;      /* Altura total aproximada de las tarjetas */
    --card-img-ratio: 2/3;     /* Ratio de aspecto para las imágenes de tarjetas */
}

/* Reseteo y estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Fondo oscuro para toda la página, incluso durante la carga */
html {
    background-color: var(--bg-color);
}

body {
    font-family: 'Segoe UI', Helvetica, Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.5;
    min-height: 100vh;
}

/* Header principal con sliding tabs */
.main-header {
    background-color: var(--bg-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    padding: 15px 16px 0;
}

.header-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.time {
    color: var(--green-accent);
}

.status-icons {
    display: flex;
    gap: 5px;
}

.status-icons i {
    color: var(--green-accent);
    font-size: 0.8rem;
}

/* Sliding Tabs */
.sliding-tabs {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0;
    padding-bottom: 5px;
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    text-align: center;
}

.tab-item i {
    font-size: 1.4rem;
    margin-bottom: 8px;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.tab-item span {
    font-size: 0.85rem;
    color: var(--text-secondary);
    transition: color 0.3s ease;
    font-weight: 500;
}

.tab-item.active i,
.tab-item.active span {
    color: var(--green-accent);
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    height: 3px;
    background-color: var(--green-accent);
    width: calc(100% / 3);
    transition: transform 0.3s ease;
    transform: translateX(0);
    border-radius: 2px 2px 0 0;
}

/* Gaming Banner */
.gaming-banner {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d4a2d 100%);
    height: 35vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-top: 70px;
    overflow: hidden;
}

.banner-overlay {
    background: radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.banner-content {
    text-align: center;
    z-index: 2;
}

.banner-logo {
    font-size: 2.2rem;
    font-weight: bold;
    margin-bottom: 8px;
    color: white;
}

.green-text {
    color: var(--green-accent);
}

.banner-subtitle {
    font-size: 0.9rem;
    color: var(--green-accent);
    margin-bottom: 15px;
    font-weight: 600;
}

.banner-controller {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 3.5rem;
    color: var(--green-accent);
    opacity: 0.4;
}

/* Secciones de juegos */
.game-section {
    margin-bottom: 35px;
    padding: 0 16px;
}

.section-title {
    font-size: 1.3rem;
    margin-bottom: 8px;
    font-weight: bold;
}

.section-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 20px;
}

/* === NUEVO SISTEMA DE CAROUSELS === */

/* Carousel 2x2 (Recomendado para ti) */
.game-carousel-2x2 {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.game-cards-2x2 {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 15px;
}

.game-cards-2x2::-webkit-scrollbar {
    display: none;
}

.cards-group-2x2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 12px;
    min-width: calc(100vw - 40px);
    margin-right: 20px;
}

/* Carousel 2x1 (Juegos Actualizados) */
.game-carousel-2x1 {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.game-cards-list {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 15px;
}

.game-cards-list::-webkit-scrollbar {
    display: none;
}

.cards-group-2x1 {
    display: flex;
    flex-direction: column;
    gap: 12px;
    min-width: calc(100vw - 40px);
    margin-right: 20px;
}

/* Carousel 1x1 (Juegos Nuevos) */
.game-carousel-1x1 {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.game-cards-single {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 15px;
}

.game-cards-single::-webkit-scrollbar {
    display: none;
}

/* === TARJETAS DE JUEGOS === */

/* Tarjetas para carousels 2x2 y 1x1 */
.game-card {
    width: var(--card-width);
    height: var(--card-height);
    flex: 0 0 auto;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease;
    background-color: var(--bg-secondary);
}

.game-card:hover {
    transform: scale(1.05);
}

.game-poster-container {
    position: relative;
    width: 100%;
    height: 140px;
    background-color: #2c2c2c;
    overflow: hidden;
    border-radius: 12px 12px 8px 8px;
}

.game-poster {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.game-info {
    padding: 12px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.game-title {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 4px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.2;
}

.game-weight {
    font-size: 0.75rem;
    color: var(--green-accent);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.game-weight i {
    margin-right: 5px;
    font-size: 0.7rem;
}

/* === TARJETAS PARA LISTA (Juegos Actualizados) === */

.game-list-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: calc(50vw - 30px);
    margin-bottom: 12px;
}

.game-list-item:hover {
    background-color: #2a2a2a;
    transform: translateY(-2px);
}

.game-list-image {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    object-fit: cover;
    margin-right: 15px;
}

.game-list-info {
    flex: 1;
}

.game-list-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 6px;
    line-height: 1.2;
}

.game-list-date {
    color: var(--text-secondary);
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.game-list-date i {
    margin-right: 6px;
}

.game-list-size {
    color: var(--green-accent);
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.game-list-size i {
    margin-right: 6px;
}

/* === INDICADORES DE CAROUSEL === */

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 15px;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator-dot.active {
    background-color: var(--green-accent);
    transform: scale(1.2);
}

.indicator-dot:hover {
    background-color: var(--text-secondary);
}

/* Contenido de tabs */
.tab-content {
    padding-top: 25px;
    transition: opacity 0.3s ease;
}

.tab-content.hidden {
    display: none;
}

/* Secciones vacías */
.empty-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    padding: 40px 20px;
}

.empty-content {
    text-align: center;
}

.empty-content i {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.empty-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

.empty-content p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* === ESTILOS PARA INFO.HTML === */

/* Header del juego */
.game-header {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    display: flex;
    flex-direction: column;
}

.game-header-overlay {
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.7) 70%,
        rgba(18, 18, 18, 1) 100%
    );
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px 40px;
}

.game-header-content {
    text-align: center;
    z-index: 2;
    width: 100%;
    max-width: 400px;
}

.game-image-container {
    margin-bottom: 30px;
}

.game-image {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
}

.game-title-container {
    text-align: center;
}

.game-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.game-size-badge {
    display: inline-block;
    background-color: var(--green-accent);
    color: black;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Contenido del juego */
.game-content {
    background-color: var(--bg-color);
    padding: 20px 16px;
}

.game-description-section {
    margin-bottom: 25px;
}

.game-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
}

/* Botones de acción */
.game-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.btn-download {
    background-color: var(--green-accent);
    color: black;
    border: none;
    padding: 15px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s ease, background-color 0.2s ease;
}

.btn-download:hover {
    background-color: #00e639;
    transform: translateY(-2px);
}

.btn-download i {
    margin-right: 10px;
}

.btn-tutorial {
    background-color: transparent;
    color: var(--green-accent);
    border: 2px solid var(--green-accent);
    padding: 15px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-tutorial:hover {
    background-color: rgba(0, 255, 65, 0.1);
}

.btn-tutorial i {
    margin-right: 10px;
}

/* Requisitos mínimos */
.requirements-section {
    margin-bottom: 30px;
}

.requirements-section h2 {
    color: var(--green-accent);
    font-size: 1.3rem;
    margin-bottom: 20px;
    font-weight: bold;
}

.requirements-grid {
    display: flex;
    justify-content: space-around;
    gap: 20px;
}

.requirement-item {
    text-align: center;
    flex: 1;
}

.requirement-icon {
    margin-bottom: 10px;
}

.requirement-icon i {
    font-size: 2rem;
    color: var(--green-accent);
}

.requirement-info {
    display: flex;
    flex-direction: column;
}

.requirement-label {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 5px;
    font-weight: 600;
}

.requirement-value {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Sección de popularidad */
.popularity-section {
    margin-bottom: 30px;
    text-align: center;
}

.popularity-badge {
    display: inline-block;
    background-color: var(--bg-secondary);
    padding: 15px 25px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.popularity-label {
    background-color: var(--green-accent);
    color: black;
    padding: 5px 15px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.8rem;
    margin-right: 10px;
}

.downloads-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Sección de juegos relacionados */
.related-games-section {
    margin-bottom: 30px;
}

.related-games-section h2 {
    font-size: 1.3rem;
    margin-bottom: 16px;
    font-weight: bold;
}

/* Responsive Design */
@media (min-width: 768px) {
    :root {
        --card-width: 160px;
        --card-height: 220px;
    }

    .banner-logo {
        font-size: 2.8rem;
    }

    .game-image {
        width: 250px;
        height: 250px;
    }

    .game-title {
        font-size: 2.5rem;
    }

    .requirements-grid {
        justify-content: center;
        gap: 40px;
    }

    .game-actions {
        flex-direction: row;
        max-width: 400px;
        margin: 0 auto 30px;
    }

    .cards-group-2x2 {
        min-width: calc(50vw - 20px);
    }

    .cards-group-2x1 {
        min-width: calc(50vw - 20px);
    }

    .game-list-item {
        min-width: calc(25vw - 20px);
    }
}

@media (min-width: 1024px) {
    :root {
        --card-width: 180px;
        --card-height: 240px;
    }

    .gaming-banner {
        height: 40vh;
    }

    .game-header {
        height: 80vh;
    }

    .game-header-content {
        max-width: 500px;
    }

    .cards-group-2x2 {
        min-width: calc(33vw - 20px);
    }

    .cards-group-2x1 {
        min-width: calc(33vw - 20px);
    }

    .game-list-item {
        min-width: calc(20vw - 20px);
    }
}