<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#121212">
    <title>GameHub - Información</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header con imagen de fondo del juego -->
    <header class="game-header">
        <div class="header-status">
            <span class="time">9:41</span>
            <div class="status-icons">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
        <div class="game-header-overlay">
            <div class="game-header-content">
                <!-- Imagen del juego centrada -->
                <div class="game-image-container">
                    <img id="game-image" class="game-image" src="" alt="">
                </div>

                <!-- Título del juego -->
                <div class="game-title-container">
                    <h1 id="game-title" class="game-title"></h1>
                    <div class="game-size-badge" id="game-size">
                        <span>PESO: 550MB</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="game-content">
        <!-- Descripción del juego -->
        <section class="game-description-section">
            <div class="game-description" id="game-description">
                <!-- La descripción se cargará dinámicamente -->
            </div>
        </section>

        <!-- Botones de acción -->
        <section class="game-actions">
            <button class="btn-download" id="btn-download">
                <i class="fas fa-download"></i> Descargar
            </button>
            <button class="btn-tutorial" id="btn-tutorial">
                <i class="fas fa-question-circle"></i> Tutorial
            </button>
        </section>

        <!-- Requisitos mínimos -->
        <section class="requirements-section">
            <h2>Requisitos mínimos</h2>
            <div class="requirements-grid">
                <div class="requirement-item">
                    <div class="requirement-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="requirement-info">
                        <span class="requirement-label">Memoria</span>
                        <span class="requirement-value" id="req-memoria">1.5 GB</span>
                    </div>
                </div>
                <div class="requirement-item">
                    <div class="requirement-icon">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="requirement-info">
                        <span class="requirement-label">Ram</span>
                        <span class="requirement-value" id="req-ram">1 GB</span>
                    </div>
                </div>
                <div class="requirement-item">
                    <div class="requirement-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="requirement-info">
                        <span class="requirement-label">Procesador</span>
                        <span class="requirement-value" id="req-procesador">Todas las Gamas</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Sección de popularidad -->
        <section class="popularity-section">
            <div class="popularity-badge" id="popularity-badge">
                <span class="popularity-label">POPULAR</span>
                <span class="downloads-count" id="downloads-count">Más de 1M descargas</span>
            </div>
        </section>

        <!-- Más juegos relacionados -->
        <section class="related-games-section">
            <h2 id="related-games-title">Más juegos similares</h2>
            <div class="game-carousel">
                <div class="game-cards" id="relacionados-container">
                    <!-- Los juegos relacionados se cargarán dinámicamente -->
                </div>
            </div>
        </section>
    </main>

    <script src="js/data.js"></script>
    <script src="js/info.js"></script>
</body>
</html> 