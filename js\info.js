/**
 * Funcionalidad específica para la página de información de juegos y apps
 */

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', inicializarPaginaInfo);

// Almacena el juego/app actual
let itemActual = null;

/**
 * Inicializa la página de información
 */
async function inicializarPaginaInfo() {
    // Cargar datos de juegos y apps
    await cargarDatos();

    // Obtener ID del item de la URL
    const idItem = obtenerParametroURL('id');
    if (!idItem) {
        // Si no hay ID, redirigir a la página principal
        window.location.href = 'index.html';
        return;
    }

    // Obtener datos del item
    itemActual = obtenerItemPorId(idItem);
    if (!itemActual) {
        // Si no se encuentra el item, redirigir a la página principal
        window.location.href = 'index.html';
        return;
    }

    // Renderizar información del item
    renderizarInfoItem();

    // Renderizar descripción
    renderizarDescripcion();

    // Renderizar requisitos
    renderizarRequisitos();

    // Renderizar sección de popularidad
    renderizarPopularidad();

    // Cargar items relacionados
    cargarItemsRelacionados();

    // Configurar eventos
    configurarEventos();
}

/**
 * Renderiza la información general del item
 */
function renderizarInfoItem() {
    // Configurar header con imagen de fondo del item
    const header = document.querySelector('.game-header');
    if (header) {
        header.style.backgroundImage = `url('${itemActual.imagen}')`;
    }

    // Establecer la imagen del juego/app
    const gameImage = document.getElementById('game-image');
    if (gameImage) {
        gameImage.src = itemActual.imagen;
        gameImage.alt = itemActual.nombre;
    }

    // Establecer el título
    const gameTitle = document.getElementById('game-title');
    if (gameTitle) {
        gameTitle.textContent = itemActual.nombre;
    }

    // Establecer el tamaño/peso
    const gameSize = document.getElementById('game-size');
    if (gameSize) {
        gameSize.querySelector('span').textContent = `PESO: ${itemActual.peso}`;
    }

    // Actualizar título de items relacionados
    const relatedTitle = document.getElementById('related-games-title');
    if (relatedTitle) {
        const tipo = itemActual.categoria === 'juego' ? 'juegos' : 'aplicaciones';
        relatedTitle.textContent = `Más ${tipo} similares`;
    }
}

/**
 * Renderiza la descripción del item
 */
function renderizarDescripcion() {
    const descripcionContainer = document.getElementById('game-description');
    if (descripcionContainer) {
        descripcionContainer.textContent = itemActual.descripcion;
    }
}

/**
 * Renderiza los requisitos mínimos
 */
function renderizarRequisitos() {
    const reqMemoria = document.getElementById('req-memoria');
    const reqRam = document.getElementById('req-ram');
    const reqProcesador = document.getElementById('req-procesador');

    if (reqMemoria) reqMemoria.textContent = itemActual.requisitos.memoria;
    if (reqRam) reqRam.textContent = itemActual.requisitos.ram;
    if (reqProcesador) reqProcesador.textContent = itemActual.requisitos.procesador;
}

/**
 * Renderiza la sección de popularidad
 */
function renderizarPopularidad() {
    const popularityBadge = document.getElementById('popularity-badge');
    const downloadsCount = document.getElementById('downloads-count');

    if (popularityBadge && downloadsCount) {
        if (itemActual.esPopular) {
            popularityBadge.style.display = 'block';
            downloadsCount.textContent = `Más de ${itemActual.descargas} descargas`;
        } else {
            popularityBadge.style.display = 'none';
        }
    }
}

/**
 * Carga los items relacionados
 */
function cargarItemsRelacionados() {
    const itemsRelacionados = obtenerItemsRelacionados(itemActual.id, 6);
    const contenedor = document.getElementById('relacionados-container');
    if (contenedor) {
        renderizarItemsCarrusel(itemsRelacionados, contenedor);
    }
}

/**
 * Renderiza items en formato carrusel
 * @param {Array} items Array de objetos de items
 * @param {HTMLElement} contenedor Elemento DOM donde se renderizarán los items
 */
function renderizarItemsCarrusel(items, contenedor) {
    contenedor.innerHTML = '';

    items.forEach(item => {
        const gameCard = document.createElement('div');
        gameCard.className = 'game-card';
        gameCard.addEventListener('click', () => {
            window.location.href = `info.html?id=${item.id}`;
        });

        // Contenedor para la imagen
        const posterContainer = document.createElement('div');
        posterContainer.className = 'game-poster-container';

        const img = document.createElement('img');
        img.className = 'game-poster';
        img.src = item.imagen;
        img.alt = item.nombre;
        img.loading = 'lazy';

        posterContainer.appendChild(img);

        const gameInfo = document.createElement('div');
        gameInfo.className = 'game-info';

        const title = document.createElement('h3');
        title.className = 'game-title';
        title.textContent = item.nombre;

        const weight = document.createElement('p');
        weight.className = 'game-weight';
        weight.innerHTML = `<i class="fas fa-download"></i> ${item.peso}`;

        gameInfo.appendChild(title);
        gameInfo.appendChild(weight);

        gameCard.appendChild(posterContainer);
        gameCard.appendChild(gameInfo);

        contenedor.appendChild(gameCard);
    });
}

/**
 * Configura los eventos específicos de la página
 */
function configurarEventos() {
    // Configurar botón de descarga
    const btnDownload = document.getElementById('btn-download');
    if (btnDownload) {
        btnDownload.addEventListener('click', () => {
            // Simular descarga (en una app real, aquí iría la lógica de descarga)
            alert(`Iniciando descarga de ${itemActual.nombre}`);
        });
    }

    // Configurar botón de tutorial
    const btnTutorial = document.getElementById('btn-tutorial');
    if (btnTutorial) {
        btnTutorial.addEventListener('click', () => {
            // Simular tutorial (en una app real, aquí iría la lógica del tutorial)
            alert(`Abriendo tutorial de ${itemActual.nombre}`);
        });
    }
}

/**
 * Utilidad para obtener parámetros de la URL
 * @param {String} nombre Nombre del parámetro
 * @returns {String|null} Valor del parámetro o null si no existe
 */
function obtenerParametroURL(nombre) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(nombre);
} 